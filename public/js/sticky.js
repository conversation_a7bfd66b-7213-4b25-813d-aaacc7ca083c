/**
 * 处理粘性元素的滚动行为
 */

// 增强的跟随页面滚动功能，解决滚动到底部的问题和布局混乱问题
document.addEventListener('DOMContentLoaded', function() {
  // 处理事件容器
  handleStickyElement('events-container', 'events-sticky', 'events-placeholder');

  // 处理右侧边栏元素
  handleRightSidebarSticky();
  
  function handleStickyElement(containerId, elementId, placeholderId) {
    const container = document.getElementById(containerId);
    const stickyElement = document.getElementById(elementId);
    const placeholder = document.getElementById(placeholderId);
    
    if (!container || !stickyElement || !placeholder) return;
    
    // 设置占位符的初始高度和宽度，与粘性元素相同
    placeholder.style.height = stickyElement.offsetHeight + 'px';
    placeholder.style.width = stickyElement.offsetWidth + 'px';
    
    // 计算容器的位置信息
    let containerRect = container.getBoundingClientRect();
    let containerTop = containerRect.top + window.pageYOffset;
    let containerBottom = containerRect.bottom + window.pageYOffset;
    let containerHeight = containerRect.height;
    let stickyHeight = stickyElement.offsetHeight;
    
    // 监听窗口大小变化，更新位置信息
    window.addEventListener('resize', function() {
      containerRect = container.getBoundingClientRect();
      containerTop = containerRect.top + window.pageYOffset;
      containerBottom = containerRect.bottom + window.pageYOffset;
      containerHeight = containerRect.height;
      stickyHeight = stickyElement.offsetHeight;
      
      // 更新占位符尺寸
      placeholder.style.height = stickyElement.offsetHeight + 'px';
      placeholder.style.width = stickyElement.offsetWidth + 'px';
      
      // 触发滚动事件以更新位置
      window.dispatchEvent(new Event('scroll'));
    });
    
    // 监听滚动事件
    window.addEventListener('scroll', function() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const viewportHeight = window.innerHeight;
      
      // 计算元素应该处于的状态
      if (scrollTop < containerTop - 20) {
        // 1. 还没滚动到容器顶部，保持正常流
        stickyElement.style.position = '';  // 清除position属性，使用CSS默认值
        stickyElement.style.top = '';
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '';  // 清除z-index，使用CSS默认值
        placeholder.classList.remove('show-placeholder');
      }
      else if (scrollTop + stickyHeight + 20 >= containerBottom) {
        // 2. 滚动到容器底部，固定在容器底部
        stickyElement.style.position = 'absolute';
        stickyElement.style.top = '0';  // 设置为0，因为已经在CSS中设置了margin-top: 20px
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '100';  // 确保不会覆盖弹出框
        placeholder.classList.add('show-placeholder');
      }
      else {
        // 3. 在容器中间滚动，固定在视口顶部
        stickyElement.style.position = 'fixed';
        stickyElement.style.top = '20px';  // 保持20px的顶部间距
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '100';  // 确保不会覆盖弹出框
        placeholder.classList.add('show-placeholder');
      }
    });
    
    // 初始触发一次滚动事件
    window.dispatchEvent(new Event('scroll'));
  }

  // 处理右侧边栏粘性元素的函数
  function handleRightSidebarSticky() {
    // 获取所有需要处理的元素
    const float1 = document.querySelector('.float1');
    const float2 = document.querySelector('.float2');
    const newsAds = document.querySelectorAll('.news-ad');

    // 创建一个包含所有右侧元素的数组
    const rightSideElements = [];

    if (float1) {
      rightSideElements.push({
        element: float1,
        originalParent: float1.parentNode,
        originalNextSibling: float1.nextSibling,
        type: 'sidebar'
      });
    }

    if (float2) {
      rightSideElements.push({
        element: float2,
        originalParent: float2.parentNode,
        originalNextSibling: float2.nextSibling,
        type: 'sidebar'
      });
    }

    newsAds.forEach(function(newsAd) {
      rightSideElements.push({
        element: newsAd,
        originalParent: newsAd.parentNode,
        originalNextSibling: newsAd.nextSibling,
        type: 'news-ad'
      });
    });

    if (rightSideElements.length === 0) return;

    // 创建粘性容器
    const stickyContainer = document.createElement('div');
    stickyContainer.className = 'right-sidebar-sticky-container';

    // 计算正确的右侧位置
    function updateStickyPosition() {
      const bodyWidth = 1240; // 页面主体宽度
      const viewportWidth = window.innerWidth;
      const rightOffset = Math.max(20, (viewportWidth - bodyWidth) / 2 + 20);

      stickyContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: ${rightOffset}px;
        width: 260px;
        z-index: 99;
        display: none;
        pointer-events: auto;
      `;
    }

    updateStickyPosition();

    // 将粘性容器添加到body
    document.body.appendChild(stickyContainer);

    // 为每个元素创建占位符
    rightSideElements.forEach(function(item) {
      const placeholder = document.createElement('div');
      placeholder.className = 'sticky-placeholder-' + item.type;
      placeholder.style.cssText = `
        width: ${item.element.offsetWidth}px;
        height: ${item.element.offsetHeight}px;
        display: none;
      `;

      // 在原位置插入占位符
      item.originalParent.insertBefore(placeholder, item.originalNextSibling);
      item.placeholder = placeholder;
    });

    let isSticky = false;

    // 滚动事件处理
    function handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const mainBanner = document.querySelector('.main-banner');

      if (!mainBanner) return;

      const mainBannerRect = mainBanner.getBoundingClientRect();
      const mainBannerTop = mainBannerRect.top + scrollTop;

      // 判断是否应该激活粘性效果 - 当main-banner滚动出视口时激活
      const shouldBeSticky = scrollTop > mainBannerTop + mainBannerRect.height - 20;

      if (shouldBeSticky && !isSticky) {
        // 激活粘性效果
        isSticky = true;
        stickyContainer.style.display = 'block';
        stickyContainer.innerHTML = ''; // 清空容器

        rightSideElements.forEach(function(item) {
          // 显示占位符
          item.placeholder.style.display = 'block';

          // 将元素移动到粘性容器
          const clonedElement = item.element.cloneNode(true);

          // 移除可能冲突的内联样式，让CSS样式生效
          clonedElement.style.cssText = '';
          clonedElement.style.marginBottom = '20px';

          stickyContainer.appendChild(clonedElement);

          // 隐藏原始元素
          item.element.style.visibility = 'hidden';
        });

      } else if (!shouldBeSticky && isSticky) {
        // 取消粘性效果
        isSticky = false;
        stickyContainer.style.display = 'none';
        stickyContainer.innerHTML = '';

        rightSideElements.forEach(function(item) {
          // 隐藏占位符
          item.placeholder.style.display = 'none';

          // 显示原始元素
          item.element.style.visibility = 'visible';
        });
      }
    }

    // 监听滚动事件
    window.addEventListener('scroll', handleScroll);

    // 监听窗口大小变化
    window.addEventListener('resize', function() {
      // 重新计算粘性容器的位置
      updateStickyPosition();
    });

    // 初始触发一次滚动事件
    handleScroll();
  }
});
